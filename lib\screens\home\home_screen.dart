import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/savings_progress_card.dart';
import '../../widgets/stats_card.dart';
import '../../widgets/trial_card.dart';
import '../../theme/app_theme.dart';
import '../../services/auth_service.dart';
import '../../services/trial_service.dart';
import '../../models/trial.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late String _greeting;
  late String _userName;
  List<Trial> _activeTrials = [];
  List<Trial> _expiringTrials = [];
  TrialStats? _stats;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    final user = AuthService.getCurrentUser();
    _userName = user?.name ?? 'User';
    _greeting = _getGreeting();

    _loadTrialData();
  }

  void _loadTrialData() {
    setState(() {
      _activeTrials = TrialService.getActiveTrials();
      _expiringTrials = TrialService.getExpiringTrials(7);
      _stats = TrialService.getTrialStats();
    });
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Good morning';
    } else if (hour < 17) {
      return 'Good afternoon';
    } else {
      return 'Good evening';
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = AuthService.getCurrentUser();
    final stats = _stats;

    return Scaffold(
      appBar: GreetingAppBar(
        userName: _userName,
        greeting: _greeting,
        notificationCount: _expiringTrials.length,
        onProfileTap: () {
          // Navigate to profile
        },
        onNotificationTap: () {
          // Show notifications
        },
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          _loadTrialData();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: AppSpacing.md),

              // Savings Progress Card
              if (user != null)
                SavingsProgressCard(
                  currentSavings: stats?.monthlySavings ?? 0,
                  goalAmount: user.monthlySavingsGoal,
                  currency: user.currency,
                  onTap: () {
                    // Navigate to savings details
                  },
                ),

              // Quick Stats
              if (stats != null) ...[
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
                  child: Text(
                    'Quick Stats',
                    style: AppTextStyles.heading3,
                  ),
                ),
                const SizedBox(height: AppSpacing.sm),
                QuickStatsRow(
                  activeTrials: stats.activeTrials.toString(),
                  expiringThisWeek: stats.expiringThisWeek.toString(),
                  monthlySavings: stats.monthlySavings.toStringAsFixed(0),
                  totalSavings: stats.totalSavings.toStringAsFixed(0),
                  onActiveTrialsTap: () {
                    // Navigate to active trials
                  },
                  onExpiringTap: () {
                    // Navigate to expiring trials
                  },
                  onMonthlySavingsTap: () {
                    // Navigate to monthly savings
                  },
                  onTotalSavingsTap: () {
                    // Navigate to total savings
                  },
                ),
              ],

              const SizedBox(height: AppSpacing.lg),

              // Expiring Trials Section
              if (_expiringTrials.isNotEmpty) ...[
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
                  child: Row(
                    children: [
                      Icon(
                        Icons.warning,
                        color: AppTheme.warningOrange,
                        size: 20,
                      ),
                      const SizedBox(width: AppSpacing.sm),
                      Text(
                        'Expiring Soon',
                        style: AppTextStyles.heading3.copyWith(
                          color: AppTheme.warningOrange,
                        ),
                      ),
                      const Spacer(),
                      TextButton(
                        onPressed: () {
                          // Navigate to all expiring trials
                        },
                        child: const Text('View All'),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: AppSpacing.sm),
                SizedBox(
                  height: 200,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
                    itemCount: _expiringTrials.length,
                    itemBuilder: (context, index) {
                      final trial = _expiringTrials[index];
                      return SizedBox(
                        width: 300,
                        child: TrialCard(
                          trial: trial,
                          onTap: () => _showTrialDetails(trial),
                          onCancel: () => _cancelTrial(trial),
                          onSnooze: () => _snoozeTrial(trial),
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(height: AppSpacing.lg),
              ],

              // Active Trials Section
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
                child: Row(
                  children: [
                    Text(
                      'Active Trials',
                      style: AppTextStyles.heading3,
                    ),
                    const Spacer(),
                    TextButton(
                      onPressed: () {
                        // Navigate to all active trials
                      },
                      child: const Text('View All'),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppSpacing.sm),

              // Active Trials List
              if (_activeTrials.isEmpty)
                _buildEmptyState()
              else
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _activeTrials.take(5).length,
                  itemBuilder: (context, index) {
                    final trial = _activeTrials[index];
                    return TrialCard(
                      trial: trial,
                      onTap: () => _showTrialDetails(trial),
                      onCancel: () => _cancelTrial(trial),
                      onSnooze: () => _snoozeTrial(trial),
                    );
                  },
                ),

              const SizedBox(height: AppSpacing.xxl),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          // Navigate to add trial screen
        },
        icon: const Icon(Icons.add),
        label: const Text('Add Trial'),
        backgroundColor: AppTheme.primaryGreen,
        foregroundColor: Colors.white,
      ),
    );
  }

  Widget _buildEmptyState() {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Column(
        children: [
          Icon(
            Icons.subscriptions_outlined,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            'No Active Trials',
            style: AppTextStyles.heading3.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            'Add your first trial to start tracking and saving money!',
            style: AppTextStyles.bodyMedium.copyWith(
              color: Colors.grey.shade500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.lg),
          ElevatedButton.icon(
            onPressed: () {
              // Navigate to add trial screen
            },
            icon: const Icon(Icons.add),
            label: const Text('Add Your First Trial'),
          ),
        ],
      ),
    );
  }

  void _showTrialDetails(Trial trial) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: const BorderRadius.vertical(
              top: Radius.circular(AppBorderRadius.xl),
            ),
          ),
          child: Column(
            children: [
              // Handle
              Container(
                margin: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Content
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.all(AppSpacing.lg),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Trial header
                      Row(
                        children: [
                          Container(
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(AppBorderRadius.md),
                              color: Colors.grey.shade100,
                            ),
                            child: trial.logoUrl != null
                                ? ClipRRect(
                                    borderRadius: BorderRadius.circular(AppBorderRadius.md),
                                    child: Image.network(
                                      trial.logoUrl!,
                                      fit: BoxFit.cover,
                                      errorBuilder: (context, error, stackTrace) =>
                                          const Icon(Icons.subscriptions),
                                    ),
                                  )
                                : const Icon(Icons.subscriptions),
                          ),
                          const SizedBox(width: AppSpacing.md),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  trial.serviceName,
                                  style: AppTextStyles.heading2,
                                ),
                                Text(
                                  trial.serviceCategory,
                                  style: AppTextStyles.bodyMedium.copyWith(
                                    color: AppTheme.textSecondary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: AppSpacing.lg),

                      // Trial details
                      _buildDetailRow('Start Date', DateFormat('MMM d, y').format(trial.startDate)),
                      _buildDetailRow('Trial Duration', '${trial.trialDurationDays} days'),
                      _buildDetailRow('Expires', DateFormat('MMM d, y').format(trial.expiryDate)),
                      _buildDetailRow('Days Remaining', '${trial.daysRemaining} days'),
                      _buildDetailRow('Monthly Cost', '\$${trial.monthlyCost.toStringAsFixed(2)}'),

                      if (trial.notes != null && trial.notes!.isNotEmpty) ...[
                        const SizedBox(height: AppSpacing.md),
                        Text(
                          'Notes',
                          style: AppTextStyles.heading3,
                        ),
                        const SizedBox(height: AppSpacing.sm),
                        Text(
                          trial.notes!,
                          style: AppTextStyles.bodyMedium,
                        ),
                      ],

                      const SizedBox(height: AppSpacing.xl),

                      // Action buttons
                      Row(
                        children: [
                          Expanded(
                            child: OutlinedButton(
                              onPressed: () {
                                Navigator.pop(context);
                                _snoozeTrial(trial);
                              },
                              child: const Text('Snooze'),
                            ),
                          ),
                          const SizedBox(width: AppSpacing.md),
                          Expanded(
                            flex: 2,
                            child: ElevatedButton(
                              onPressed: () {
                                Navigator.pop(context);
                                _cancelTrial(trial);
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppTheme.urgentRed,
                              ),
                              child: const Text('Cancel Trial'),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppSpacing.xs),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _cancelTrial(Trial trial) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Trial'),
        content: Text(
          'Are you sure you want to cancel ${trial.serviceName}? This will save you \$${trial.monthlyCost.toStringAsFixed(2)}/month.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Keep Trial'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.urgentRed,
            ),
            child: const Text('Cancel Trial'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await TrialService.cancelTrial(trial.id);
      _loadTrialData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${trial.serviceName} trial cancelled! You saved \$${trial.monthlyCost.toStringAsFixed(2)}/month.'),
            backgroundColor: AppTheme.successGreen,
          ),
        );
      }
    }
  }

  Future<void> _snoozeTrial(Trial trial) async {
    final snoozeDays = await showDialog<int>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Snooze Reminder'),
        content: const Text('How many days would you like to snooze this reminder?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, 1),
            child: const Text('1 Day'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, 3),
            child: const Text('3 Days'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, 7),
            child: const Text('1 Week'),
          ),
        ],
      ),
    );

    if (snoozeDays != null) {
      await TrialService.snoozeTrialReminder(trial.id, snoozeDays);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${trial.serviceName} reminder snoozed for $snoozeDays days.'),
          ),
        );
      }
    }
  }
}
