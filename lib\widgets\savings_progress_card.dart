import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

class SavingsProgressCard extends StatelessWidget {
  final double currentSavings;
  final double goalAmount;
  final String currency;
  final VoidCallback? onTap;

  const SavingsProgressCard({
    super.key,
    required this.currentSavings,
    required this.goalAmount,
    this.currency = 'USD',
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final progress = goalAmount > 0 ? (currentSavings / goalAmount).clamp(0.0, 1.0) : 0.0;
    final isGoalAchieved = currentSavings >= goalAmount;

    return Card(
      margin: const EdgeInsets.all(AppSpacing.md),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppBorderRadius.lg),
        child: Container(
          padding: const EdgeInsets.all(AppSpacing.lg),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppBorderRadius.lg),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: isGoalAchieved
                  ? [
                      AppTheme.successGreen.withOpacity(0.1),
                      AppTheme.celebrationGold.withOpacity(0.1),
                    ]
                  : [
                      AppTheme.primaryGreen.withOpacity(0.05),
                      AppTheme.accentGreen.withOpacity(0.05),
                    ],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Icon(
                    isGoalAchieved ? Icons.celebration : Icons.savings,
                    color: isGoalAchieved ? AppTheme.celebrationGold : AppTheme.primaryGreen,
                    size: 28,
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  Expanded(
                    child: Text(
                      isGoalAchieved ? 'Goal Achieved! 🎉' : 'Monthly Savings Goal',
                      style: AppTextStyles.heading3.copyWith(
                        color: isGoalAchieved ? AppTheme.successGreen : AppTheme.primaryGreen,
                      ),
                    ),
                  ),
                  if (onTap != null)
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: AppTheme.getTextSecondary(context),
                    ),
                ],
              ),
              
              const SizedBox(height: AppSpacing.lg),
              
              // Savings amount
              Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '\$${currentSavings.toStringAsFixed(0)}',
                    style: AppTextStyles.heading1.copyWith(
                      color: isGoalAchieved ? AppTheme.successGreen : AppTheme.primaryGreen,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Text(
                      'of \$${goalAmount.toStringAsFixed(0)}',
                      style: AppTextStyles.bodyLargeSecondary(context),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: AppSpacing.md),
              
              // Progress bar
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '${(progress * 100).toStringAsFixed(0)}% Complete',
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.w600,
                          color: isGoalAchieved ? AppTheme.successGreen : AppTheme.primaryGreen,
                        ),
                      ),
                      if (goalAmount > currentSavings)
                        Text(
                          '\$${(goalAmount - currentSavings).toStringAsFixed(0)} to go',
                          style: AppTextStyles.bodySmallSecondary(context),
                        ),
                    ],
                  ),
                  const SizedBox(height: AppSpacing.sm),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(AppBorderRadius.circular),
                    child: LinearProgressIndicator(
                      value: progress,
                      backgroundColor: AppTheme.getSurfaceVariant(context),
                      valueColor: AlwaysStoppedAnimation<Color>(
                        isGoalAchieved ? AppTheme.successGreen : AppTheme.primaryGreen,
                      ),
                      minHeight: 8,
                    ),
                  ),
                ],
              ),
              
              if (isGoalAchieved) ...[
                const SizedBox(height: AppSpacing.md),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSpacing.md,
                    vertical: AppSpacing.sm,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.successGreen.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppBorderRadius.md),
                    border: Border.all(
                      color: AppTheme.successGreen.withOpacity(0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: AppTheme.successGreen,
                        size: 20,
                      ),
                      const SizedBox(width: AppSpacing.sm),
                      Expanded(
                        child: Text(
                          'Congratulations! You\'ve reached your savings goal this month.',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppTheme.successGreen,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
